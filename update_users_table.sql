-- تحديث جدول المستخدمين لإضافة ميزة ربط الموظفين
-- تشغيل هذا الملف لإضافة العمود المطلوب لربط المستخدمين بالموظفين

USE employees_db;

-- إضا<PERSON>ة عمود employee_id لربط المستخدمين بالموظفين
ALTER TABLE users ADD COLUMN employee_id INT NULL AFTER role;

-- إضافة المفتاح الخارجي
ALTER TABLE users ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL;

-- إضافة فهرس لتحسين الأداء
CREATE INDEX idx_users_employee_id ON users(employee_id);

COMMIT;

-- ملاحظات:
-- 1. العمود employee_id يسمح بالقيم NULL (للمستخدمين غير المرتبطين بموظفين)
-- 2. عند حذف موظف، سيتم تعيين employee_id إلى NULL في جدول المستخدمين
-- 3. هذا التحديث آمن ولن يؤثر على البيانات الموجودة
