<?php
require_once 'config.php';
checkAdmin(); // التحقق من صلاحيات المدير

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $username = sanitize($_POST['username']);
                $password = $_POST['password'];
                $role = $_POST['role'];
                
                if (empty($username) || empty($password) || empty($role)) {
                    $message = 'جميع الحقول مطلوبة';
                    $messageType = 'danger';
                } elseif (strlen($password) < 6) {
                    $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    $messageType = 'danger';
                } else {
                    try {
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
                        $stmt->execute([$username, $hashedPassword, $role]);
                        $message = 'تم إضافة المستخدم بنجاح';
                    } catch(PDOException $e) {
                        if ($e->getCode() == 23000) {
                            $message = 'اسم المستخدم موجود مسبقاً';
                            $messageType = 'danger';
                        } else {
                            $message = 'خطأ في إضافة المستخدم';
                            $messageType = 'danger';
                        }
                    }
                }
                break;

            case 'add_employee_account':
                // التحقق من وجود عمود employee_id
                $checkColumn = $pdo->query("SHOW COLUMNS FROM users LIKE 'employee_id'");
                if ($checkColumn->rowCount() == 0) {
                    $message = 'يجب تحديث قاعدة البيانات أولاً لاستخدام هذه الميزة';
                    $messageType = 'danger';
                    break;
                }

                $employee_id = intval($_POST['employee_id']);
                $username = sanitize($_POST['username']);
                $password = $_POST['password'];

                if (empty($username) || empty($password)) {
                    $message = 'اسم المستخدم وكلمة المرور مطلوبان';
                    $messageType = 'danger';
                } elseif (strlen($password) < 6) {
                    $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    $messageType = 'danger';
                } else {
                    try {
                        // التحقق من عدم وجود حساب للموظف
                        $checkStmt = $pdo->prepare("SELECT username FROM users WHERE employee_id = ?");
                        $checkStmt->execute([$employee_id]);
                        $existingUser = $checkStmt->fetch();

                        if ($existingUser) {
                            $message = 'يوجد حساب بالفعل لهذا الموظف: ' . $existingUser['username'];
                            $messageType = 'warning';
                        } else {
                            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("INSERT INTO users (username, password, role, employee_id) VALUES (?, ?, 'user', ?)");
                            $stmt->execute([$username, $hashedPassword, $employee_id]);
                            $message = 'تم إنشاء حساب للموظف بنجاح';
                        }
                    } catch(PDOException $e) {
                        if ($e->getCode() == 23000) {
                            $message = 'اسم المستخدم موجود مسبقاً';
                            $messageType = 'danger';
                        } else {
                            $message = 'خطأ في إنشاء الحساب: ' . $e->getMessage();
                            $messageType = 'danger';
                        }
                    }
                }
                break;

            case 'update_database':
                try {
                    // التحقق من عدم وجود العمود
                    $checkColumn = $pdo->query("SHOW COLUMNS FROM users LIKE 'employee_id'");
                    if ($checkColumn->rowCount() > 0) {
                        $message = 'قاعدة البيانات محدثة بالفعل';
                        $messageType = 'info';
                    } else {
                        // تنفيذ التحديث
                        $pdo->exec("ALTER TABLE users ADD COLUMN employee_id INT NULL AFTER role");
                        $pdo->exec("ALTER TABLE users ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL");
                        $pdo->exec("CREATE INDEX idx_users_employee_id ON users(employee_id)");
                        $message = 'تم تحديث قاعدة البيانات بنجاح! يمكنك الآن استخدام ميزة ربط المستخدمين بالموظفين.';
                        $messageType = 'success';

                        // إعادة تحميل الصفحة لتحديث البيانات
                        header('Location: users.php?updated=1');
                        exit();
                    }
                } catch (Exception $e) {
                    $message = 'خطأ في تحديث قاعدة البيانات: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;

            case 'reset_password':
                $user_id = intval($_POST['user_id']);
                $new_password = $_POST['new_password'];
                
                if (empty($new_password)) {
                    $message = 'كلمة المرور الجديدة مطلوبة';
                    $messageType = 'danger';
                } elseif (strlen($new_password) < 6) {
                    $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    $messageType = 'danger';
                } else {
                    try {
                        $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ? AND id != ?");
                        $stmt->execute([$hashedPassword, $user_id, $_SESSION['user_id']]);
                        
                        if ($stmt->rowCount() > 0) {
                            $message = 'تم إعادة تعيين كلمة المرور بنجاح';
                        } else {
                            $message = 'لا يمكن إعادة تعيين كلمة مرور حسابك الخاص';
                            $messageType = 'danger';
                        }
                    } catch(PDOException $e) {
                        $message = 'خطأ في إعادة تعيين كلمة المرور';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'delete':
                $user_id = intval($_POST['user_id']);
                
                if ($user_id == $_SESSION['user_id']) {
                    $message = 'لا يمكن حذف حسابك الخاص';
                    $messageType = 'danger';
                } else {
                    try {
                        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                        $stmt->execute([$user_id]);
                        $message = 'تم حذف المستخدم بنجاح';
                    } catch(PDOException $e) {
                        $message = 'خطأ في حذف المستخدم';
                        $messageType = 'danger';
                    }
                }
                break;
        }
    }
}

// التحقق من وجود عمود employee_id في جدول users
$hasEmployeeIdColumn = false;
try {
    $checkColumn = $pdo->query("SHOW COLUMNS FROM users LIKE 'employee_id'");
    $hasEmployeeIdColumn = $checkColumn->rowCount() > 0;
} catch (Exception $e) {
    $hasEmployeeIdColumn = false;
}

// جلب المستخدمين مع إحصائيات الموظفين
if ($hasEmployeeIdColumn) {
    // إذا كان العمود موجود، استخدم الاستعلام المحدث
    $stmt = $pdo->query("
        SELECT u.*,
               COUNT(e.id) as employees_count,
               emp.name as linked_employee_name,
               emp.employee_id as linked_employee_id
        FROM users u
        LEFT JOIN employees e ON u.id = e.user_id
        LEFT JOIN employees emp ON u.employee_id = emp.id
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");

    // جلب الموظفين الذين ليس لديهم حسابات
    $employeesStmt = $pdo->query("
        SELECT e.*
        FROM employees e
        LEFT JOIN users u ON e.id = u.employee_id
        WHERE u.employee_id IS NULL
        ORDER BY e.name
    ");
    $employeesWithoutAccounts = $employeesStmt->fetchAll();
} else {
    // إذا لم يكن العمود موجود، استخدم الاستعلام القديم
    $stmt = $pdo->query("
        SELECT u.*,
               COUNT(e.id) as employees_count,
               NULL as linked_employee_name,
               NULL as linked_employee_id
        FROM users u
        LEFT JOIN employees e ON u.id = e.user_id
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");

    // جلب جميع الموظفين (لأنه لا يمكن التحقق من الربط)
    $employeesStmt = $pdo->query("SELECT * FROM employees ORDER BY name");
    $employeesWithoutAccounts = $employeesStmt->fetchAll();
}

$users = $stmt->fetchAll();

// رسالة نجاح التحديث
if (isset($_GET['updated']) && $_GET['updated'] == '1') {
    $message = 'تم تحديث قاعدة البيانات بنجاح! يمكنك الآن استخدام جميع الميزات الجديدة.';
    $messageType = 'success';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .role-badge {
            font-size: 0.85rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees.php">
                    <i class="fas fa-users me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="attendance.php">
                    <i class="fas fa-calendar-alt me-1"></i>الحضور
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                        <span class="badge bg-warning text-dark ms-1">مدير</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-cog me-2"></i>إدارة المستخدمين</h2>
                    <div>
                        <a href="link_users.php" class="btn btn-info me-2">
                            <i class="fas fa-link me-2"></i>ربط المستخدمين بالموظفين
                        </a>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                    <?php echo showMessage($message, $messageType); ?>
                <?php endif; ?>

                <?php if (!$hasEmployeeIdColumn): ?>
                    <div class="alert alert-warning">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحديث مطلوب:</strong> لاستخدام ميزة ربط المستخدمين بالموظفين، يجب تحديث قاعدة البيانات.
                                <br>
                                <small class="text-muted">يمكنك تحديث قاعدة البيانات تلقائياً أو يدوياً من phpMyAdmin</small>
                            </div>
                            <form method="POST" class="ms-3">
                                <input type="hidden" name="action" value="update_database">
                                <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('هل تريد تحديث قاعدة البيانات؟')">
                                    <i class="fas fa-database me-1"></i>تحديث تلقائي
                                </button>
                            </form>
                        </div>
                        <hr>
                        <details>
                            <summary class="text-muted" style="cursor: pointer;">أوامر التحديث اليدوي</summary>
                            <div class="mt-2">
                                <code>ALTER TABLE users ADD COLUMN employee_id INT NULL AFTER role;</code><br>
                                <code>ALTER TABLE users ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL;</code>
                            </div>
                        </details>
                    </div>
                <?php endif; ?>

                <!-- إنشاء حسابات للموظفين -->
                <?php if ($hasEmployeeIdColumn && !empty($employeesWithoutAccounts)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-user-plus me-2"></i>إنشاء حسابات للموظفين</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">الموظفين التاليين ليس لديهم حسابات في النظام. يمكنك إنشاء حساب لأي منهم لتمكينه من رؤية رصيده وإجازاته:</p>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الموظف</th>
                                        <th>معرف الموظف</th>
                                        <th>الشعبة</th>
                                        <th>الرصيد</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($employeesWithoutAccounts as $employee): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-user me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($employee['name']); ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($employee['employee_id']); ?></td>
                                            <td><?php echo htmlspecialchars($employee['department']); ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $employee['balance']; ?> يوم</span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-success"
                                                        onclick="showCreateAccountModal(<?php echo $employee['id']; ?>, '<?php echo htmlspecialchars($employee['name']); ?>')">
                                                    <i class="fas fa-plus me-1"></i>إنشاء حساب
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>الدور</th>
                                        <th>الموظف المرتبط</th>
                                        <th>عدد الموظفين</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-user me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($user['username']); ?>
                                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge bg-info ms-2">أنت</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['role'] == 'admin'): ?>
                                                    <span class="badge role-badge bg-warning text-dark">
                                                        <i class="fas fa-crown me-1"></i>مدير
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge role-badge bg-primary">
                                                        <i class="fas fa-user me-1"></i>مستخدم
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['linked_employee_name']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-link me-1"></i>
                                                        <?php echo htmlspecialchars($user['linked_employee_name']); ?>
                                                        <small>(<?php echo htmlspecialchars($user['linked_employee_id']); ?>)</small>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">غير مرتبط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $user['employees_count']; ?> موظف
                                                </span>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <button class="btn btn-sm btn-outline-warning me-1" 
                                                            onclick="resetPassword(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', <?php echo $user['employees_count']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يمكن التعديل</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                        </div>
                        <div class="mb-3">
                            <label for="role" class="form-label">الدور</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="user">مستخدم عادي</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج إعادة تعيين كلمة المرور -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reset_password">
                        <input type="hidden" name="user_id" id="reset_user_id">
                        <p>إعادة تعيين كلمة المرور للمستخدم: <strong id="reset_username"></strong></p>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">إعادة تعيين كلمة المرور</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج حذف مستخدم -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="user_id" id="delete_user_id">
                        <p>هل أنت متأكد من حذف المستخدم <strong id="delete_username"></strong>؟</p>
                        <div class="alert alert-warning" id="delete_warning" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            سيتم حذف <span id="employees_count"></span> موظف وجميع سجلات الحضور المرتبطة بهم
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج إنشاء حساب للموظف -->
    <div class="modal fade" id="createAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء حساب للموظف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_employee_account">
                        <input type="hidden" name="employee_id" id="create_employee_id">

                        <div class="mb-3">
                            <label class="form-label">الموظف</label>
                            <input type="text" class="form-control" id="create_employee_name" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="create_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="create_username" name="username" required>
                            <div class="form-text">سيتم استخدام هذا الاسم لتسجيل الدخول</div>
                        </div>

                        <div class="mb-3">
                            <label for="create_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="create_password" name="password" required minlength="6">
                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم إنشاء حساب بصلاحيات "مستخدم" يمكن الموظف من رؤية رصيده وإجازاته فقط.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>إنشاء الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showCreateAccountModal(employeeId, employeeName) {
            document.getElementById('create_employee_id').value = employeeId;
            document.getElementById('create_employee_name').value = employeeName;
            document.getElementById('create_username').value = ''; // مسح القيم السابقة
            document.getElementById('create_password').value = '';
            new bootstrap.Modal(document.getElementById('createAccountModal')).show();
        }

        function resetPassword(userId, username) {
            document.getElementById('reset_user_id').value = userId;
            document.getElementById('reset_username').textContent = username;
            document.getElementById('new_password').value = '';
            new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
        }

        function deleteUser(userId, username, employeesCount) {
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_username').textContent = username;

            const warningDiv = document.getElementById('delete_warning');
            if (employeesCount > 0) {
                document.getElementById('employees_count').textContent = employeesCount;
                warningDiv.style.display = 'block';
            } else {
                warningDiv.style.display = 'none';
            }

            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }
    </script>
</body>
</html>
